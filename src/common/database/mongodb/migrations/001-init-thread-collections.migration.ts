import { Connection } from 'mongoose';
import { IMigration } from '../migration.interface';

export class InitThreadCollectionsMigration implements IMigration {
  name = 'InitThreadCollections';
  version = '001';

  async up(connection: Connection): Promise<void> {
    const db = connection.db!;

    // Create threads collection
    await db.createCollection('threads');
    const threadsCollection = db.collection('threads');

    // Create indexes for threads
    await threadsCollection.createIndex({ title: 1 });
    await threadsCollection.createIndex({ groupAssignmentId: 1 });
    await threadsCollection.createIndex({ threadUserIds: 1 });
    await threadsCollection.createIndex({ createdBy: 1 });
    await threadsCollection.createIndex({ updatedAt: -1 });

    // Compound indexes for threads
    await threadsCollection.createIndex({
      groupAssignmentId: 1,
      updatedAt: -1,
    });
    await threadsCollection.createIndex({ createdBy: 1, createdAt: -1 });

    console.log('✓ Created threads collection with indexes');

    // Create messages collection
    await db.createCollection('messages');
    const messagesCollection = db.collection('messages');

    // Create indexes for messages
    await messagesCollection.createIndex({ threadId: 1, timestamp: 1 });
    await messagesCollection.createIndex({ senderId: 1 });

    // Text index for message content search
    await messagesCollection.createIndex({ content: 'text' });

    console.log('✓ Created messages collection with indexes');
  }

  async down(connection: Connection): Promise<void> {
    const db = connection.db!;

    // Drop collections
    await db.dropCollection('messages');
    console.log('✓ Dropped messages collection');

    await db.dropCollection('threads');
    console.log('✓ Dropped threads collection');
  }
}
