import { Message } from '@modules/threads/entities/thread.entity';

interface MockUser {
  id: string;
  name: string;
  email: string;
}

export async function seedMessages(
  threads: any[],
  users: MockUser[],
): Promise<any[]> {
  console.log('🌱 Seeding messages...');

  // Check if messages already exist
  const existingMessages = await Message.find();
  if (existingMessages.length > 0) {
    console.log('✅ Messages already exist, skipping message seeding');
    return existingMessages;
  }

  const instructor = users.find((u) => u.name === 'Dr. <PERSON>')!;
  const alice = users.find((u) => u.name === '<PERSON>')!;
  const bob = users.find((u) => u.name === '<PERSON>')!;
  const charlie = users.find((u) => u.name === '<PERSON>')!;
  const diana = users.find((u) => u.name === '<PERSON>')!;
  const eve = users.find((u) => u.name === '<PERSON>')!;

  const messagesData = [];

  // Messages for Thread 1: Project Planning & Architecture Discussion
  const thread1 = threads[0];
  messagesData.push(
    {
      threadId: thread1._id.toString(),
      senderId: instructor.id,
      content:
        "Welcome everyone! Let's start by discussing the overall architecture for our e-commerce platform. What are your initial thoughts on the tech stack?",
      timestamp: new Date('2024-01-15T09:00:00Z'),
    },
    {
      threadId: thread1._id.toString(),
      senderId: alice.id,
      content:
        'I think we should use React for the frontend and Node.js with Express for the backend. PostgreSQL would be good for the database.',
      timestamp: new Date('2024-01-15T09:15:00Z'),
    },
    {
      threadId: thread1._id.toString(),
      senderId: bob.id,
      content:
        'Agreed on the tech stack! Should we also consider using Redis for caching and session management?',
      timestamp: new Date('2024-01-15T09:30:00Z'),
    },
    {
      threadId: thread1._id.toString(),
      senderId: instructor.id,
      content:
        "Excellent suggestions! Redis is definitely a good addition. Let's also think about the microservices architecture vs monolithic approach.",
      timestamp: new Date('2024-01-15T10:00:00Z'),
    },
  );

  // Messages for Thread 2: Frontend Development Coordination
  const thread2 = threads[1];
  messagesData.push(
    {
      threadId: thread2._id.toString(),
      senderId: alice.id,
      content:
        "Hi team! I've created this thread to coordinate our frontend development. Let's divide the components among ourselves.",
      timestamp: new Date('2024-01-16T10:00:00Z'),
    },
    {
      threadId: thread2._id.toString(),
      senderId: bob.id,
      content:
        'Great idea! I can work on the product catalog and search functionality.',
      timestamp: new Date('2024-01-16T10:15:00Z'),
    },
    {
      threadId: thread2._id.toString(),
      senderId: charlie.id,
      content:
        "I'll take care of the shopping cart and checkout process components.",
      timestamp: new Date('2024-01-16T10:30:00Z'),
    },
    {
      threadId: thread2._id.toString(),
      senderId: alice.id,
      content:
        "Perfect! I'll handle the user authentication, profile management, and the main layout components.",
      timestamp: new Date('2024-01-16T11:00:00Z'),
    },
  );

  // Messages for Thread 3: Backend API Development
  const thread3 = threads[2];
  messagesData.push(
    {
      threadId: thread3._id.toString(),
      senderId: bob.id,
      content:
        "Let's discuss the API endpoints we need. I'm thinking we need user management, product management, and order processing APIs.",
      timestamp: new Date('2024-01-17T14:00:00Z'),
    },
    {
      threadId: thread3._id.toString(),
      senderId: diana.id,
      content:
        "Don't forget about payment processing and inventory management APIs. We should also implement proper authentication middleware.",
      timestamp: new Date('2024-01-17T14:20:00Z'),
    },
    {
      threadId: thread3._id.toString(),
      senderId: instructor.id,
      content:
        'Good thinking! Make sure to implement proper error handling and validation for all endpoints. Consider using JWT for authentication.',
      timestamp: new Date('2024-01-17T15:00:00Z'),
    },
  );

  // Messages for Thread 4: Database Schema Design
  const thread4 = threads[3];
  messagesData.push(
    {
      threadId: thread4._id.toString(),
      senderId: instructor.id,
      content:
        "Let's start designing our library management database schema. What are the main entities we need to consider?",
      timestamp: new Date('2024-01-18T09:00:00Z'),
    },
    {
      threadId: thread4._id.toString(),
      senderId: charlie.id,
      content:
        'We need Books, Authors, Members, Loans, and Categories as main entities. Should we also include Publishers?',
      timestamp: new Date('2024-01-18T09:20:00Z'),
    },
    {
      threadId: thread4._id.toString(),
      senderId: diana.id,
      content:
        'Yes, Publishers would be useful. We should also consider Staff/Librarians, Reservations, and Fine/Penalty entities.',
      timestamp: new Date('2024-01-18T09:45:00Z'),
    },
  );

  // Messages for Thread 5: Query Optimization Strategies
  const thread5 = threads[4];
  messagesData.push(
    {
      threadId: thread5._id.toString(),
      senderId: charlie.id,
      content:
        "I've been researching indexing strategies. We should definitely index frequently searched fields like book titles and author names.",
      timestamp: new Date('2024-01-19T11:00:00Z'),
    },
    {
      threadId: thread5._id.toString(),
      senderId: eve.id,
      content:
        'Good point! We should also consider composite indexes for queries that filter by multiple fields, like searching books by category and availability.',
      timestamp: new Date('2024-01-19T11:30:00Z'),
    },
    {
      threadId: thread5._id.toString(),
      senderId: diana.id,
      content:
        "Don't forget about indexing the loan dates and member IDs for quick lookup of overdue books and member history.",
      timestamp: new Date('2024-01-19T12:00:00Z'),
    },
  );

  // Messages for Thread 6: Data Preprocessing Pipeline
  const thread6 = threads[5];
  messagesData.push(
    {
      threadId: thread6._id.toString(),
      senderId: instructor.id,
      content:
        "Let's discuss our data preprocessing pipeline. What are the main steps we need to implement for our predictive analytics model?",
      timestamp: new Date('2024-01-20T10:00:00Z'),
    },
    {
      threadId: thread6._id.toString(),
      senderId: diana.id,
      content:
        'We need data cleaning, handling missing values, feature scaling, and encoding categorical variables. Should we also implement feature selection?',
      timestamp: new Date('2024-01-20T10:25:00Z'),
    },
    {
      threadId: thread6._id.toString(),
      senderId: eve.id,
      content:
        'Feature selection would be great! We should also consider outlier detection and removal. I can work on the data cleaning module.',
      timestamp: new Date('2024-01-20T10:45:00Z'),
    },
  );

  // Messages for Thread 7: Model Selection & Training
  const thread7 = threads[6];
  messagesData.push(
    {
      threadId: thread7._id.toString(),
      senderId: diana.id,
      content:
        "I've been researching different algorithms. For our predictive analytics, we should consider Random Forest, XGBoost, and Neural Networks.",
      timestamp: new Date('2024-01-21T13:00:00Z'),
    },
    {
      threadId: thread7._id.toString(),
      senderId: eve.id,
      content:
        'Great choices! We should also try Linear Regression and SVM as baseline models. Cross-validation will be important for model selection.',
      timestamp: new Date('2024-01-21T13:30:00Z'),
    },
    {
      threadId: thread7._id.toString(),
      senderId: alice.id,
      content:
        'I agree on cross-validation. Should we also implement hyperparameter tuning using GridSearch or RandomSearch?',
      timestamp: new Date('2024-01-21T14:00:00Z'),
    },
  );

  // Messages for Thread 8: Performance Evaluation & Metrics
  const thread8 = threads[7];
  messagesData.push(
    {
      threadId: thread8._id.toString(),
      senderId: eve.id,
      content:
        "Let's define our evaluation metrics. For our predictive model, we should use accuracy, precision, recall, and F1-score.",
      timestamp: new Date('2024-01-22T09:00:00Z'),
    },
    {
      threadId: thread8._id.toString(),
      senderId: alice.id,
      content:
        "Don't forget about ROC-AUC and confusion matrix visualization. We should also track training time and model complexity.",
      timestamp: new Date('2024-01-22T09:30:00Z'),
    },
    {
      threadId: thread8._id.toString(),
      senderId: bob.id,
      content:
        'Good points! We should also implement learning curves to visualize model performance over different training set sizes.',
      timestamp: new Date('2024-01-22T10:00:00Z'),
    },
    {
      threadId: thread8._id.toString(),
      senderId: instructor.id,
      content:
        'Excellent discussion! Make sure to document all metrics and create comprehensive performance reports for each model.',
      timestamp: new Date('2024-01-22T10:30:00Z'),
    },
  );

  const savedMessages = [];
  for (const messageData of messagesData) {
    const message = new Message(messageData);
    const savedMessage = await message.save();
    savedMessages.push(savedMessage);
  }

  console.log(`✅ Created ${savedMessages.length} messages`);
  return savedMessages;
}
