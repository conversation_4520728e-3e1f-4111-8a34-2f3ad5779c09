import { Thread } from '@modules/threads/entities/thread.entity';

interface MockUser {
  id: string;
  name: string;
  email: string;
}

interface MockGroup {
  id: string;
  name: string;
}

export async function seedThreads(
  users: MockUser[],
  groups: MockGroup[],
): Promise<any[]> {
  console.log('🌱 Seeding threads...');

  // Check if threads already exist
  const existingThreads = await Thread.find();
  if (existingThreads.length > 0) {
    console.log('✅ Threads already exist, skipping thread seeding');
    return existingThreads;
  }

  const instructor = users.find((u) => u.name === 'Dr. <PERSON>')!;
  const students = users.filter((u) => u.name !== 'Dr. <PERSON>');

  const threadsData = [
    // Group 1 threads - E-commerce Platform
    {
      title: 'Project Planning & Architecture Discussion',
      description:
        "Let's discuss the overall architecture and plan for our e-commerce platform project.",
      groupId: groups[0].id,
      userId: [
        instructor.id,
        students[0].id, // Alice
        students[1].id, // <PERSON>
      ],
      createdBy: instructor.id,
    },
    {
      title: 'Frontend Development Coordination',
      description:
        'Coordination thread for frontend development tasks and React components.',
      groupId: groups[0].id,
      userId: [
        students[0].id, // <PERSON>
        students[1].id, // Bob
        students[2].id, // Charlie
      ],
      createdBy: students[0].id,
    },
    {
      title: 'Backend API Development',
      description:
        'Discussion about backend API endpoints, database schema, and Node.js implementation.',
      groupId: groups[0].id,
      userId: [
        instructor.id,
        students[1].id, // Bob
        students[3].id, // Diana
      ],
      createdBy: students[1].id,
    },

    // Group 2 threads - Library Management
    {
      title: 'Database Schema Design',
      description:
        "Let's design the database schema for our library management system.",
      groupId: groups[1].id,
      userId: [
        instructor.id,
        students[2].id, // Charlie
        students[3].id, // Diana
      ],
      createdBy: instructor.id,
    },
    {
      title: 'Query Optimization Strategies',
      description:
        'Discussion about indexing strategies and query optimization for the library system.',
      groupId: groups[1].id,
      userId: [
        students[2].id, // Charlie
        students[3].id, // Diana
        students[4].id, // Eve
      ],
      createdBy: students[2].id,
    },

    // Group 3 threads - ML Model Development
    {
      title: 'Data Preprocessing Pipeline',
      description:
        'Planning the data preprocessing and feature engineering pipeline.',
      groupId: groups[2].id,
      userId: [
        instructor.id,
        students[3].id, // Diana
        students[4].id, // Eve
      ],
      createdBy: instructor.id,
    },
    {
      title: 'Model Selection & Training',
      description:
        'Discussion about different ML algorithms and model training strategies.',
      groupId: groups[2].id,
      userId: [
        students[3].id, // Diana
        students[4].id, // Eve
        students[0].id, // Alice
      ],
      createdBy: students[3].id,
    },
    {
      title: 'Performance Evaluation & Metrics',
      description:
        "Let's discuss evaluation metrics and performance benchmarks for our models.",
      groupId: groups[2].id,
      userId: [
        instructor.id,
        students[4].id, // Eve
        students[0].id, // Alice
        students[1].id, // Bob
      ],
      createdBy: students[4].id,
    },
  ];

  const savedThreads = [];
  for (const threadData of threadsData) {
    const thread = new Thread(threadData);
    const savedThread = await thread.save();
    savedThreads.push(savedThread);
  }

  console.log(`✅ Created ${savedThreads.length} threads`);
  return savedThreads;
}
