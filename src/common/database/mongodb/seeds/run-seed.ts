#!/usr/bin/env ts-node

import { NestFactory } from '@nestjs/core';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { Module } from '@nestjs/common';
import mongoose from 'mongoose';
import mongodbConfig from '../config';
import { seedThreads } from './thread.seed';
import { seedMessages } from './message.seed';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [mongodbConfig],
      envFilePath: ['.env'],
    }),
    MongooseModule.forRootAsync({
      useFactory: (configService: ConfigService) => ({
        uri: configService.get('mongodb.uri'),
        dbName: configService.get('mongodb.dbName'),
        retryWrites: true,
        writeConcern: { w: 'majority' },
        retryAttempts: 5,
        retryDelay: 1000,
      }),
      inject: [ConfigService],
    }),
  ],
})
class MongoSeedModule {}

async function runMongoSeeds() {
  console.log('🚀 Starting MongoDB seeding...');

  try {
    const app = await NestFactory.create(MongoSeedModule);
    const configService = app.get(ConfigService);

    // Ensure connection is established
    const mongoUri = configService.get('mongodb.uri');
    if (mongoose.connection.readyState !== 1) {
      await mongoose.connect(mongoUri);
    }

    console.log('✅ MongoDB connection established');

    // Get existing relational data for reference
    // Note: In a real scenario, you might want to fetch this from the relational DB
    // For now, we'll use mock data that should match your existing seeds

    const mockUsers = [
      {
        id: '550e8400-e29b-41d4-a716-446655440001',
        name: 'Dr. John',
        email: '<EMAIL>',
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440002',
        name: 'Alice Johnson',
        email: '<EMAIL>',
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440003',
        name: 'Bob Smith',
        email: '<EMAIL>',
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440004',
        name: 'Charlie Brown',
        email: '<EMAIL>',
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440005',
        name: 'Diana Prince',
        email: '<EMAIL>',
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440006',
        name: 'Eve Wilson',
        email: '<EMAIL>',
      },
    ];

    const mockGroups = [
      {
        id: '660e8400-e29b-41d4-a716-446655440001',
        name: 'Final Project - E-commerce Platform',
      },
      {
        id: '660e8400-e29b-41d4-a716-446655440002',
        name: 'Database Design Project - Library Management',
      },
      {
        id: '660e8400-e29b-41d4-a716-446655440003',
        name: 'ML Model Development - Predictive Analytics',
      },
    ];

    // Seed data in the correct order
    const threads = await seedThreads(mockUsers, mockGroups);
    const messages = await seedMessages(threads, mockUsers);

    console.log('\n🎉 MongoDB seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   💬 Threads: ${threads.length}`);
    console.log(`   📝 Messages: ${messages.length}`);

    await mongoose.connection.close();
    console.log('✅ MongoDB connection closed');
    process.exit(0);
  } catch (err) {
    console.error('❌ Error while running MongoDB seeders:', err);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  void runMongoSeeds();
}

export { runMongoSeeds };
