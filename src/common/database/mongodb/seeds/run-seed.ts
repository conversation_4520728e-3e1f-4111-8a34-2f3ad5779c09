#!/usr/bin/env ts-node

import mongoose from 'mongoose';
import { Thread, Message } from '@modules/threads/entities/thread.entity';

// Mock data that should match your existing relational database seeds
const mockUsers = [
  {
    id: '550e8400-e29b-41d4-a716-446655440001',
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440002',
    name: '<PERSON>',
    email: '<EMAIL>',
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440003',
    name: '<PERSON>',
    email: '<EMAIL>',
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440004',
    name: '<PERSON>',
    email: '<EMAIL>',
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440005',
    name: '<PERSON>',
    email: '<EMAIL>',
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440006',
    name: '<PERSON>',
    email: '<EMAIL>',
  },
];

const mockGroups = [
  {
    id: '660e8400-e29b-41d4-a716-446655440001',
    name: 'Final Project - E-commerce Platform',
  },
  {
    id: '660e8400-e29b-41d4-a716-446655440002',
    name: 'Database Design Project - Library Management',
  },
  {
    id: '660e8400-e29b-41d4-a716-446655440003',
    name: 'ML Model Development - Predictive Analytics',
  },
];

async function seedThreads() {
  console.log('🌱 Seeding threads...');

  // Check if threads already exist
  const existingThreads = await Thread.find();
  if (existingThreads.length > 0) {
    console.log('✅ Threads already exist, skipping thread seeding');
    return existingThreads;
  }

  const instructor = mockUsers.find((u) => u.name === 'Dr. John')!;
  const students = mockUsers.filter((u) => u.name !== 'Dr. John');

  const threadsData = [
    // Group 1 threads - E-commerce Platform
    {
      title: 'Project Planning & Architecture Discussion',
      description:
        "Let's discuss the overall architecture and plan for our e-commerce platform project.",
      groupAssignmentId: mockGroups[0].id,
      threadUserIds: [
        instructor.id,
        students[0].id, // Alice
        students[1].id, // Bob
      ],
      createdBy: instructor.id,
    },
    {
      title: 'Frontend Development Coordination',
      description:
        'Coordination thread for frontend development tasks and React components.',
      groupAssignmentId: mockGroups[0].id,
      threadUserIds: [
        students[0].id, // Alice
        students[1].id, // Bob
        students[2].id, // Charlie
      ],
      createdBy: students[0].id,
    },
    {
      title: 'Backend API Development',
      description:
        'Discussion about backend API endpoints, database schema, and Node.js implementation.',
      groupAssignmentId: mockGroups[0].id,
      threadUserIds: [
        instructor.id,
        students[1].id, // Bob
        students[3].id, // Diana
      ],
      createdBy: students[1].id,
    },

    // Group 2 threads - Library Management
    {
      title: 'Database Schema Design',
      description:
        "Let's design the database schema for our library management system.",
      groupAssignmentId: mockGroups[1].id,
      threadUserIds: [
        instructor.id,
        students[2].id, // Charlie
        students[3].id, // Diana
      ],
      createdBy: instructor.id,
    },
    {
      title: 'Query Optimization Strategies',
      description:
        'Discussion about indexing strategies and query optimization for the library system.',
      groupAssignmentId: mockGroups[1].id,
      threadUserIds: [
        students[2].id, // Charlie
        students[3].id, // Diana
        students[4].id, // Eve
      ],
      createdBy: students[2].id,
    },

    // Group 3 threads - ML Model Development
    {
      title: 'Data Preprocessing Pipeline',
      description:
        'Planning the data preprocessing and feature engineering pipeline.',
      groupAssignmentId: mockGroups[2].id,
      threadUserIds: [
        instructor.id,
        students[3].id, // Diana
        students[4].id, // Eve
      ],
      createdBy: instructor.id,
    },
    {
      title: 'Model Selection & Training',
      description:
        'Discussion about different ML algorithms and model training strategies.',
      groupAssignmentId: mockGroups[2].id,
      threadUserIds: [
        students[3].id, // Diana
        students[4].id, // Eve
        students[0].id, // Alice
      ],
      createdBy: students[3].id,
    },
    {
      title: 'Performance Evaluation & Metrics',
      description:
        "Let's discuss evaluation metrics and performance benchmarks for our models.",
      groupAssignmentId: mockGroups[2].id,
      threadUserIds: [
        instructor.id,
        students[4].id, // Eve
        students[0].id, // Alice
        students[1].id, // Bob
      ],
      createdBy: students[4].id,
    },
  ];

  const savedThreads: any[] = [];
  for (const threadData of threadsData) {
    const thread = new Thread(threadData);
    const savedThread = await thread.save();
    savedThreads.push(savedThread);
  }

  console.log(`✅ Created ${savedThreads.length} threads`);
  return savedThreads;
}

async function seedMessages(threads: any[]) {
  console.log('🌱 Seeding messages...');

  // Check if messages already exist
  const existingMessages = await Message.find();
  if (existingMessages.length > 0) {
    console.log('✅ Messages already exist, skipping message seeding');
    return existingMessages;
  }

  const instructor = mockUsers.find((u) => u.name === 'Dr. John')!;
  const alice = mockUsers.find((u) => u.name === 'Alice Johnson')!;
  const bob = mockUsers.find((u) => u.name === 'Bob Smith')!;
  const charlie = mockUsers.find((u) => u.name === 'Charlie Brown')!;
  const diana = mockUsers.find((u) => u.name === 'Diana Prince')!;
  const eve = mockUsers.find((u) => u.name === 'Eve Wilson')!;

  const messagesData: any[] = [];

  // Messages for Thread 1: Project Planning & Architecture Discussion
  if (threads[0]) {
    const thread1 = threads[0];
    messagesData.push(
      {
        threadId: thread1._id.toString(),
        senderId: instructor.id,
        content:
          "Welcome everyone! Let's start by discussing the overall architecture for our e-commerce platform. What are your initial thoughts on the tech stack?",
        timestamp: new Date('2024-01-15T09:00:00Z'),
      },
      {
        threadId: thread1._id.toString(),
        senderId: alice.id,
        content:
          'I think we should use React for the frontend and Node.js with Express for the backend. PostgreSQL would be good for the database.',
        timestamp: new Date('2024-01-15T09:15:00Z'),
      },
      {
        threadId: thread1._id.toString(),
        senderId: bob.id,
        content:
          'Agreed on the tech stack! Should we also consider using Redis for caching and session management?',
        timestamp: new Date('2024-01-15T09:30:00Z'),
      },
      {
        threadId: thread1._id.toString(),
        senderId: instructor.id,
        content:
          "Excellent suggestions! Redis is definitely a good addition. Let's also think about the microservices architecture vs monolithic approach.",
        timestamp: new Date('2024-01-15T10:00:00Z'),
      },
    );
  }

  // Messages for Thread 2: Frontend Development Coordination
  if (threads[1]) {
    const thread2 = threads[1];
    messagesData.push(
      {
        threadId: thread2._id.toString(),
        senderId: alice.id,
        content:
          "Hi team! I've created this thread to coordinate our frontend development. Let's divide the components among ourselves.",
        timestamp: new Date('2024-01-16T10:00:00Z'),
      },
      {
        threadId: thread2._id.toString(),
        senderId: bob.id,
        content:
          'Great idea! I can work on the product catalog and search functionality.',
        timestamp: new Date('2024-01-16T10:15:00Z'),
      },
      {
        threadId: thread2._id.toString(),
        senderId: charlie.id,
        content:
          "I'll take care of the shopping cart and checkout process components.",
        timestamp: new Date('2024-01-16T10:30:00Z'),
      },
    );
  }

  const savedMessages: any[] = [];
  for (const messageData of messagesData) {
    const message = new Message(messageData);
    const savedMessage = await message.save();
    savedMessages.push(savedMessage);
  }

  console.log(`✅ Created ${savedMessages.length} messages`);
  return savedMessages;
}

async function runMongoSeeds() {
  console.log('🚀 Starting MongoDB seeding...');

  try {
    // Ensure connection is established
    const mongoUri =
      process.env.MONGODB_URI ||
      '*************************************************************************';
    if (
      mongoose.connection.readyState !== mongoose.ConnectionStates.connected
    ) {
      await mongoose.connect(mongoUri);
    }

    console.log('✅ MongoDB connection established');

    // Seed data in the correct order
    const threads = await seedThreads();

    console.log('\n🎉 MongoDB seeding completed successfully!');
    console.log('\n📊 Summary:');
    console.log(`   💬 Threads: ${threads.length}`);

    await mongoose.connection.close();
    console.log('✅ MongoDB connection closed');
    process.exit(0);
  } catch (err) {
    console.error('❌ Error while running MongoDB seeders:', err);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  void runMongoSeeds();
}

export { runMongoSeeds };
